<template>
  <view class="index-page">
    <!-- 欢迎页面 - 仅在首次启动时显示 -->
    <view v-if="showWelcomePage" class="welcome-content">
      <text class="welcome-icon">🌸</text>
      <text class="welcome-title">开启心灵成长之旅</text>
      <text class="welcome-description">每一次倾诉都是一次疗愈，每一次聆听都是一次成长</text>

      <!-- 功能按钮区 -->
      <view class="action-buttons">
        <button class="primary-button" @click="startCounseling">开始咨询</button>
        <!-- 测试按钮，仅在开发环境显示 -->
        <button class="secondary-button" @click="resetNavigationState" v-if="isDevelopment">重置状态</button>
      </view>
    </view>

    <!-- 心理咨询页面 - 从其他页面切回时显示 -->
    <view v-else class="counseling-content">
      <!-- 引入心理咨询页面的内容 -->
      <counseling-sessions ref="counselingSessions"></counseling-sessions>
    </view>
  </view>
</template>

<script>
import counselingSessions from '../counseling/sessions.vue'

export default {
  components: {
    counselingSessions
  },
  data() {
    return {
      showWelcomePage: true, // 默认显示欢迎页面
      isDevelopment: process.env.NODE_ENV === 'development' // 开发环境标识
    }
  },
  onLoad() {
    console.log('首页加载')
    this.checkNavigationState()
  },
  onShow() {
    console.log('首页显示')
    // 检查是否是通过tabBar切换回来的
    this.checkTabBarNavigation()
    this.checkNavigationState()
  },
  methods: {
    // 检查是否是通过tabBar导航切换回来的
    checkTabBarNavigation() {
      try {
        // 获取当前页面栈
        const pages = getCurrentPages()
        console.log('当前页面栈长度:', pages.length)

        // 如果页面栈只有一个页面，说明是通过tabBar切换的
        if (pages.length === 1) {
          const hasNavigated = uni.getStorageSync('hasNavigatedFromHome')
          if (hasNavigated) {
            console.log('检测到tabBar导航，标记为已导航状态')
            // 通过tabBar切换回来，且之前已经导航过，设置为显示心理咨询页面
            this.showWelcomePage = false
            return
          }
        }
      } catch (error) {
        console.error('检查tabBar导航失败:', error)
      }
    },

    // 检查导航状态，决定显示哪个页面
    checkNavigationState() {
      try {
        // 检查是否是首次启动
        const hasNavigated = uni.getStorageSync('hasNavigatedFromHome')
        console.log('导航状态检查:', { hasNavigated })

        if (hasNavigated) {
          // 如果用户已经从首页导航过，显示心理咨询页面
          this.showWelcomePage = false
          console.log('显示心理咨询页面')
        } else {
          // 首次启动，显示欢迎页面
          this.showWelcomePage = true
          console.log('显示欢迎页面')
        }
      } catch (error) {
        console.error('检查导航状态失败:', error)
        // 出错时默认显示欢迎页面
        this.showWelcomePage = true
      }
    },

    startCounseling() {
      // 标记用户已经从首页导航
      try {
        uni.setStorageSync('hasNavigatedFromHome', true)
        console.log('设置导航标记成功')
      } catch (error) {
        console.error('设置导航标记失败:', error)
      }

      // 使用navigateTo跳转到咨询页面
      uni.navigateTo({
        url: '/pages/counseling/sessions'
      }).catch(err => {
        console.error('跳转失败:', err);
      })
    },

    // 重置导航状态（仅用于测试）
    resetNavigationState() {
      try {
        uni.removeStorageSync('hasNavigatedFromHome')
        this.showWelcomePage = true
        console.log('导航状态已重置')
        uni.showToast({
          title: '状态已重置',
          icon: 'success'
        })
      } catch (error) {
        console.error('重置导航状态失败:', error)
      }
    }
  }
}
</script>

<style lang="scss">
@import '../../styles/global.scss';

/* 响应式单位转换 */
$rpx-ratio: 2; /* 设计稿1px = 2rpx */

@function rpx($value) {
  @return $value * $rpx-ratio * 1rpx;
}

/* 首页容器 */
.index-page {
  min-height: 100vh;
  background: #ffffff;
}

/* 心理咨询内容区域 */
.counseling-content {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* 欢迎内容 */
.welcome-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: rpx(80) rpx(40);
  text-align: center;
  background-color: var(--bg-secondary);
  border-radius: rpx(40);
}

.welcome-icon {
  font-size: rpx(96);
  margin-bottom: rpx(32);
}

.welcome-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: #a98bc7;
  margin-bottom: rpx(24);
}

.welcome-description {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin-bottom: rpx(64);
  line-height: var(--line-height);
  max-width: rpx(640);
}

/* 按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: rpx(560);
  gap: rpx(32);
}

.primary-button {
  @extend .btn;
  @extend .btn-primary;
  border-radius: rpx(60);
  padding: rpx(28) rpx(64);
  font-size: var(--font-size-md);
  font-weight: 600;
  box-shadow: 0 rpx(16) rpx(40) rgba(169, 139, 199, 0.3);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  letter-spacing: rpx(1);
  position: relative;
  overflow: hidden;
}

.primary-button:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.primary-button:hover:after {
  opacity: 1;
}

.primary-button:active {
  transform: translateY(rpx(6));
  box-shadow: 0 rpx(8) rpx(20) rgba(169, 139, 199, 0.2);
}

/* 测试按钮样式 */
.secondary-button {
  @extend .btn;
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
  border-radius: rpx(60);
  padding: rpx(20) rpx(40);
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-top: rpx(16);
  transition: all 0.3s ease;
}

.secondary-button:active {
  background: #e9ecef;
  transform: translateY(rpx(2));
}
</style>